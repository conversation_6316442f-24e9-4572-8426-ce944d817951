<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Lowballer - AI-Powered Marketplace Negotiation</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/icon/logo.svg" />
    <link rel="icon" type="image/png" href="/assets/icon/logo.svg" />
    <link rel="apple-touch-icon" href="/assets/icon/logo.svg" />

    <!-- Open Graph / Social Media Preview Tags -->
    <meta
      property="og:title"
      content="Lowballer - AI-Powered Marketplace Negotiation"
    />
    <meta
      property="og:description"
      content="Lowballer leverages cutting-edge AI models to analyze listings, calculate depreciation, and determine the best buy price."
    />
    <meta
      property="og:image"
      content="https://lowballer.co/lowballer-preview.png"
    />
    <meta property="og:url" content="https://lowballer.co" />
    <meta property="og:type" content="website" />

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Lowballer - AI-Powered Marketplace Negotiation"
    />
    <meta
      name="twitter:description"
      content="Lowballer leverages cutting-edge AI models to analyze listings, calculate depreciation, and determine the best buy price."
    />
    <meta
      name="twitter:image"
      content="https://lowballer.co/lowballer-preview.png"
    />

    <link rel="stylesheet" href="/assets/styles/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body>
    <nav aria-label="Main navigation">
      <a href="/" class="logo-link">
        <div class="logo">Lowballer</div>
      </a>

      <!-- Navigation Links -->
      <div class="signup-nav-links">
        <a href="#pricing">Discord</a>
      </div>
    </nav>
    <div class="success-msg-container">
      <h1 class="signup-heading">You're all set!</h1>
      <p class="signup-subheading">
        Your account is now upgraded and ready to unlock its full potential.
      </p>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get("session_id");
        if (!sessionId) {
          // If there’s no session_id, just stop here.
          return;
        }

        // Call backend to redeem session & update MongoDB
        fetch("https://lowballer-api.vercel.app/api/redeem-checkout-session", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ sessionId }),
        })
          .then((res) => {
            if (!res.ok) throw new Error("Network response was not OK");
            return res.json();
          })
          .then((data) => {
            if (data.success && data.newTier) {
              const newTier = data.newTier;
              try {
                // Update localStorage.user.tier (or set localStorage.tier)
                const userJson = localStorage.getItem("user");
                if (userJson) {
                  const userObj = JSON.parse(userJson);
                  userObj.tier = newTier;
                  localStorage.setItem("user", JSON.stringify(userObj));
                } else {
                  localStorage.setItem("tier", newTier);
                }
              } catch (err) {
                console.warn("Could not update localStorage:", err);
                localStorage.setItem("tier", newTier);
              }
            }
            // If you want to log something, you can:
            // console.log("Redeem result:", data);
          })
          .catch((err) => {
            console.error("Error redeeming checkout session:", err);
          });
      });
    </script>
  </body>
</html>
