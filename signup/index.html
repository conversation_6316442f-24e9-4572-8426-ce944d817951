<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign Up | Lowballer</title>
    <link rel="stylesheet" href="/assets/styles/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
  </head>
  <body>
    <nav aria-label="Main navigation">
      <a href="/" class="logo-link">
        <div class="logo">Lowballer</div>
      </a>

      <!-- Navigation Links -->
      <div class="signup-nav-links">
        <a href="#pricing">Discord</a>
      </div>
    </nav>
    <main class="signup-main">
      <form id="signup-form" class="signup-form-full">
        <h1 class="signup-heading">Create an Account</h1>
        <p class="signup-subheading">
          Get started with <PERSON><PERSON><PERSON> in minutes. <br />Note that email
          verification is required for security purposes.
        </p>
        <p class="signup-subheading"></p>

        <div class="name-fields">
          <div class="input-group">
            <label class="input-label">First Name</label>
            <div class="input-container">
              <i class="fas fa-user input-icon"></i>
              <input
                type="text"
                class="signup-input-full"
                placeholder="First Name"
                name="firstName"
                required
              />
            </div>
          </div>

          <div class="input-group">
            <label class="input-label">Last Name</label>
            <div class="input-container">
              <i class="fas fa-user input-icon"></i>
              <input
                type="text"
                class="signup-input-full"
                placeholder="Last Name"
                name="lastName"
                required
              />
            </div>
          </div>
        </div>

        <div class="input-group">
          <label class="input-label">Email</label>
          <div class="input-container">
            <i class="fas fa-envelope input-icon"></i>
            <input
              type="email"
              class="signup-input-full"
              placeholder="Email Address"
              name="email"
              required
            />
          </div>
        </div>

        <div class="input-group">
          <label class="input-label">Phone</label>
          <div class="input-container">
            <i class="fas fa-phone input-icon"></i>
            <input
              type="tel"
              class="signup-input-full"
              placeholder="Phone Number"
              name="phone"
              required
            />
          </div>
        </div>

        <div class="input-group">
          <label class="input-label">Password</label>
          <div class="input-container">
            <i class="fas fa-lock input-icon"></i>
            <input
              type="password"
              class="signup-input-full"
              id="passwordInput"
              placeholder="Create a password"
              name="password"
              required
            />
            <i class="fas fa-eye password-toggle" id="togglePassword"></i>
          </div>
        </div>
        <p id="flash-message" class="error"></p>
        <p id="flash-message" class="success"></p>

        <div class="terms-checkbox">
          <input type="checkbox" id="terms" required />
          <label for="terms"
            >I have read and agree to the <a href="#">Terms & Privacy</a></label
          >
        </div>

        <button type="submit" id="signup-button" class="signup-button-full">
          <i class="fa-solid fa-spinner fa-spin-pulse hidden" id="spinner"></i>
          <span class="btn-text">Sign Up</span>
        </button>

        <p class="signup-switch-full">
          Already have an account? <a href="/login">Log in</a>
        </p>
      </form>
    </main>

    <script src="/assets/scripts/togglePassword.js"></script>
    <script src="/assets/scripts/signup.js"></script>
  </body>
</html>
