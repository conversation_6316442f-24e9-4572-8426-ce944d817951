/* Color Variables */
:root {
  --primary-bg: #01050f;
  --accent-blue: #2339a7;
  --accent-blue-dark: #152467;
  --error-message: #ff6060;
  --error-bg: #2b0b0e;
  --success-message: #2fa83d;
  --success-bg: #051a11;
  --highlight-blue: #2e4ea4;
  --text-primary: white;
  --text-secondary: #ccc;
  --text-muted: #888;
  --text-light: #dddee4;
  --input-bg: #0b1221;
  --input-border: #31415b;
  --input-placeholder: #797e80;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body {
  font-family: "Segoe UI", sans-serif;
  background-color: var(--primary-bg);
  color: var(--text-primary);
}

.disabled-btn {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}

.link {
  text-align: center;
  color: #fff;
  margin-top: 20px;
  display: block;
  position: relative;
}

#flash-message {
  margin-bottom: 12px;
}

#flash-message.success {
  color: var(--success-message);
  border: 1px solid var(--success-message);
  background-color: var(--success-bg);
  padding: 12px;
  border-radius: 10px;
  display: none;
}

#flash-message.error {
  color: var(--error-message);
  border: 1px solid var(--error-message);
  background-color: var(--error-bg);
  padding: 12px;
  border-radius: 10px;
  display: none;
}

.hidden {
  display: none !important;
}

/* --------- NAVBAR --------- */
nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 240px;
  background-color: transparent;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

nav.scrolled {
  background-color: var(--primary-bg);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo {
  font-weight: 600;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.logo-link {
  text-decoration: none;
  color: inherit;
  display: inline-block;
}

/* Hide hamburger button by default */
.hamburger-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 100;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 25px;
}

.nav-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--text-primary);
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-btn {
  background-color: #1b1e2e;
  color: var(--text-primary);
  padding: 8px 16px;
  border-radius: 10px;
  font-size: 0.9rem;
  border: none;
  cursor: pointer;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 40px;
  left: 0;
  background-color: #1b1e2e;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
  z-index: 10;
  width: 140px;
}

.dropdown-menu a {
  display: block;
  padding: 10px 16px;
  color: var(--text-primary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.dropdown.active .dropdown-menu {
  display: block;
}

/* --------- Hero Section --------- */
.hero-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 100px 20px 80px;
  background: radial-gradient(
    circle at center,
    rgba(0, 98, 255, 0.2) 0%,
    transparent 50%
  );
}

.hero-headingContainer {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
}

.hero-icon {
  width: 80px;
  height: 80px;
}

.hero-heading {
  text-align: start;
  font-size: 34px;
  font-weight: 600;
}

.hero-highlight {
  color: var(--highlight-blue);
  font-weight: 400;
  font-size: 25px;
}

.hero-subheading {
  font-size: 22px;
  color: var(--text-light);
  max-width: 500px;
  margin-top: 50px;
  font-weight: 400;
  margin-bottom: 18px;
}

.hero-subheading-secondary {
  font-size: 14px;
  line-height: 23px;
  color: var(--text-secondary);
}

.cta-group {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 50px;
}

/* Button Styles */
.cta-button {
  background: linear-gradient(
    90deg,
    var(--accent-blue),
    var(--accent-blue-dark)
  );
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
  height: 65px;
  gap: 0.5rem;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.2);
  transition: background 0.3s, box-shadow 0.3s;
}

.cta-button:hover {
  background: linear-gradient(90deg, #2563eb, #1d4ed8);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
}

.cta-button img {
  width: 35px;
  height: 35px;
}

.secondary-button {
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: 5px solid var(--highlight-blue);
  color: var(--highlight-blue);
  border-radius: 5px;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none;
  transition: border-color 0.3s, background-color 0.3s;
}

.hero-note {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-top: 20px;
}

/* --------- Feature Section --------- */
.feature-section {
  width: 100%;
  padding: 4rem 1rem;
  display: flex;
  justify-content: center;
}

.feature-container {
  width: 100%;
  max-width: 900px;
}

.feature-media {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
  background-color: #2a2a2a;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 0.75rem;
  overflow: hidden;
}

.feature-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.video-note {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
}

/* --------- Integration Section --------- */
.integration-section {
  display: flex;
  justify-content: center;
  padding: 4rem 1rem;
}

.integration-box {
  background-color: #223587;
  border-radius: 3rem;
  padding: 2rem;
  text-align: center;
  max-width: 800px;
  height: 350px;
  width: 100%;
  box-shadow: 0 0 30px rgba(58, 63, 203, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.integration-box .icons {
  font-size: 4.5rem;
  color: var(--text-primary);
  display: flex;
  justify-content: center;
  gap: 4rem;
  margin-bottom: 1.5rem;
}

.integration-box h3 {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.integration-box p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* --------- Pricing Section --------- */
.pricing-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pricing-wrapper {
  position: relative;
  width: 100%;
  max-width: 1100px;
  padding: 80px 40px;
  border-radius: 20px;
  z-index: 1;
}

.pricing-wrapper::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background: radial-gradient(
    circle at center,
    rgba(36, 107, 255, 0.2),
    transparent 70%
  );
  z-index: 0;
  border-radius: 20px;
}

.pricing-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.pricing-section_heading {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.subtitle {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-bottom: 50px;
}

.plans {
  display: flex;
  gap: 60px;
  flex-wrap: wrap;
  justify-content: center;
}

.plan {
  background-color: #111319;
  border-radius: 16px;
  padding: 30px 25px;
  width: 280px;
  text-align: center;
}

.plan.highlight {
  border: 2px solid #405f9e;
  box-shadow: 0 0 20px rgba(36, 107, 255, 0.4);
}
.plan .label {
  font-size: 0.75rem;
  color: var(--highlight-blue);
  margin-bottom: 15px;
}

.freeTrial {
  color: #87af58;
}

.plan h3 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.price {
  font-size: 2rem;
  font-weight: bold;
  margin: 10px 0 20px;
  color: var(--text-primary);
}

.features {
  list-style: none;
  padding: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 25px;
}

.features li {
  margin: 8px 0;
}

.btn {
  background: linear-gradient(
    90deg,
    var(--accent-blue),
    var(--accent-blue-dark)
  );
  color: var(--text-primary);
  padding: 10px 0;
  width: 100%;
  height: 60px;
  border: none;
  border-radius: 10px;
  font-weight: bold;
  font-size: 1.15rem;
  letter-spacing: 1px;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.btn:hover {
  opacity: 0.9;
}

a.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
}

/* --------- Final CTA Section --------- */
.final-cta-section {
  padding: 4rem 1rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

.final-cta-container {
  max-width: 720px;
  margin: 0 auto;
  color: var(--text-primary);
}

.final-cta-container h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.final-cta-container .description {
  font-size: 0.95rem;
  color: var(--highlight-blue);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.final-cta-button {
  background-color: #2953e5;
  padding: 1rem;
  border-radius: 1.5rem;
  color: var(--text-primary);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 320px;
  height: 70px;
  gap: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 2rem;
  transition: background-color 0.2s ease-in-out;
}

.final-cta-button img {
  width: 30px;
  height: 30px;
}

.final-cta-button:hover {
  background-color: #07469e;
}

.final-cta-container .disclaimer {
  font-size: 0.75rem;
  color: var(--text-muted);
  line-height: 1.4;
}

/* --------- Footer Section --------- */
.site-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  padding: 1.5rem 1rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.footer-content {
  max-width: 1100px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.footer-left {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-left-up {
  display: flex;
}

.footer-left img {
  width: 50px;
  height: 50px;
}

.footer-logo {
  color: var(--text-primary);
}

.footer-right {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-right a {
  margin-left: 1rem;
  color: var(--text-secondary);
  text-decoration: none;
}

.footer-right a:hover {
  color: var(--text-primary);
}

/* Media Queries */
@media (max-width: 1024px) {
  nav {
    padding: 20px 100px;
  }

  .feature-media {
    height: 400px;
  }

  .feature-container {
    width: 90%;
  }

  .integration-box {
    height: 300px;
    padding: 1.5rem;
  }

  .integration-box .icons {
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  nav {
    padding: 15px 60px;

    background-color: transparent;
  }

  .hero-section {
    padding: 80px 20px 60px;
  }

  .hero-heading {
    font-size: 28px;
  }

  .hero-highlight {
    font-size: 22px;
  }

  .hero-subheading {
    font-size: 20px;
    margin-top: 30px;
  }

  .feature-section {
    padding: 2rem !important;
  }

  .feature-media {
    height: 350px;
    /* height: 300px; */
  }

  .cta-group {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .cta-button,
  .secondary-button {
    width: 100%;
    max-width: 300px;
  }

  .plans {
    flex-direction: column;
    gap: 2rem;
    align-items: center;
  }

  .plan {
    width: 100%;
    max-width: 400px;
  }

  .integration-box {
    height: auto;
    padding: 1.5rem;
  }

  .integration-box .icons {
    flex-wrap: wrap;
    gap: 2rem;
  }
}

/* Mobile Devices (480px and below) */
@media (max-width: 480px) {
  /* Navigation */
  nav {
    position: sticky;
    top: 0;
    z-index: 1000;
    background-color: var(--primary-bg);
    padding: 20px 40px;
  }

  .hamburger-btn {
    display: block;
    position: relative;
    z-index: 101;
  }

  .nav-links {
    position: absolute;
    top: 100%;
    right: 20px;
    width: 150px;
    height: auto;
    background-color: #1b1e2e;
    flex-direction: column;
    align-items: flex-start;
    padding: 20px;
    gap: 15px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: translateY(-20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 100;
  }

  .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .dropdown {
    width: 80%;
  }

  .dropdown-btn {
    width: 100%;
    text-align: left;
    background: none;
    padding: 10px 0;
  }

  .dropdown-menu {
    position: static;
    width: 100%;
    box-shadow: none;
    margin-top: 10px;
    display: none;
  }

  .dropdown:hover .dropdown-menu {
    display: none;
  }

  .dropdown.active .dropdown-menu {
    display: block;
  }

  /* Hero Section */
  .hero-section {
    padding: 60px 15px 40px;
  }

  .hero-headingContainer {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .hero-heading {
    text-align: center;
    font-size: 24px;
  }

  .hero-highlight {
    font-size: 18px;
    display: block;
    margin-top: 10px;
  }

  .hero-icon {
    width: 60px;
    height: 60px;
  }

  .hero-subheading {
    font-size: 18px;
    margin-top: 30px;
    padding: 0 10px;
  }

  .hero-subheading-secondary {
    padding: 0 15px;
    font-size: 13px;
  }

  .cta-group {
    margin-top: 30px;
    width: 100%;
    padding: 0 15px;
  }

  .cta-button,
  .secondary-button {
    height: 55px;
    font-size: 16px;
  }

  /* Feature Section */
  .feature-section {
    padding: 1.5rem 15px !important;
  }

  .feature-media {
    height: 250px;
  }

  /* Integration Section */
  .integration-section {
    padding: 2rem 15px;
  }

  .integration-box {
    border-radius: 2rem;
    padding: 1.5rem;
  }

  .integration-box .icons {
    font-size: 3.5rem;
    gap: 1.5rem;
    margin-bottom: 1rem;
  }

  .integration-box h3 {
    font-size: 1.5rem;
  }

  .integration-box p {
    font-size: 0.85rem;
    padding: 0 10px;
  }

  /* Pricing Section */
  .pricing-wrapper {
    padding: 50px 20px;
  }

  .pricing-section_heading {
    font-size: 1.5rem;
  }

  .plan {
    padding: 25px 20px;
  }

  /* Final CTA */
  .final-cta-section {
    padding: 2rem 15px;
  }

  .final-cta-button {
    width: 100%;
    max-width: 300px;
    height: 60px;
    font-size: 0.95rem;
  }

  /* Footer */
  .footer-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .footer-right {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }

  .footer-right a {
    margin: 0;
  }
}

/* Small Mobile Devices (360px and below) */
@media (max-width: 360px) {
  .hero-heading {
    font-size: 22px;
  }

  .hero-highlight {
    font-size: 16px;
  }

  .hero-subheading {
    font-size: 16px;
  }

  .cta-button,
  .secondary-button {
    height: 50px;
    font-size: 15px;
  }

  .nav-links {
    width: 46%;
  }

  .integration-box .icons {
    font-size: 3rem;
  }
}

/* ========================= */
/* ===== Signup Page ======= */
/* ========================= */

.signup-nav-links {
  display: flex;
  align-items: center;
  gap: 25px;
}

.signup-nav-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.signup-main {
  background: radial-gradient(
    circle at center,
    rgba(0, 98, 255, 0.2) 0%,
    transparent 50%
  );
  background-color: var(--primary-bg);
  min-height: 85vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-primary);
  padding: 20px;
}

.signup-form-full {
  width: 100%;
  max-width: 450px;
  padding: 30px;
}

.signup-heading {
  text-align: center;
  font-size: 28px;
  margin-bottom: 15px;
  font-weight: 600;
}

.signup-subheading {
  text-align: center;
  font-size: 14px;
  color: #a0a4a6;
  margin-bottom: 30px;
}

.input-group {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.name-fields {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.name-fields .input-group {
  flex: 1;
  margin-bottom: 0;
}

.input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s;
}

.signup-input-full {
  width: 100%;
  padding: 12px 15px 12px 40px;
  background-color: var(--input-bg);
  color: var(--text-primary);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.signup-input-full:focus {
  outline: none;
  border-color: var(--highlight-blue);
  box-shadow: 0 0 0 2px rgba(46, 78, 164, 0.3);
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--input-placeholder);
  font-size: 14px;
}

.terms-checkbox {
  display: flex;
  align-items: center;
  margin: 20px 0;
  font-size: 13px;
  color: var(--text-secondary);
}

.terms-checkbox input {
  margin-right: 10px;
}

.terms-checkbox a {
  color: #377cfa;
  text-decoration: none;
}

.terms-checkbox a:hover {
  text-decoration: underline;
}

.signup-button-full {
  width: 100%;
  padding: 14px;
  background: linear-gradient(
    90deg,
    var(--accent-blue),
    var(--accent-blue-dark)
  );
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.signup-button-full:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.signup-button-full:disabled {
  opacity: 0.4;
  pointer-events: none;
}

.signup-switch-full {
  text-align: center;
  font-size: 13px;
  color: var(--text-muted);
  margin-top: 20px;
}

.signup-switch-full a {
  color: #377cfa;
  text-decoration: none;
  font-weight: 500;
}

.signup-switch-full a:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .signup-form-full {
    padding: 25px;
  }

  .name-fields {
    flex-direction: column;
    gap: 15px;
  }
}

/* ========================= */
/* ===== Login Page ======= */
/* ========================= */

.login-nav-links {
  display: flex;
  align-items: center;
  gap: 25px;
}

.login-nav-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.login-main {
  background: radial-gradient(
    circle at center,
    rgba(0, 98, 255, 0.2) 0%,
    transparent 50%
  );
  background-color: var(--primary-bg);
  min-height: 85vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-primary);
  padding: 20px;
}

.login-form {
  width: 100%;
  max-width: 450px;
  padding: 30px;
}

.login-heading {
  text-align: center;
  font-size: 28px;
  margin-bottom: 15px;
  font-weight: 600;
}

.login-subheading {
  text-align: center;
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 30px;
}

.login-input-group {
  margin-bottom: 20px;
}

.login-input-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.login-input-container {
  position: relative;
}

.login-input-field {
  width: 100%;
  padding: 12px 15px 12px 40px;
  background-color: var(--input-bg);
  color: var(--text-primary);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.login-input-field:focus {
  outline: none;
  border-color: var(--highlight-blue);
  box-shadow: 0 0 0 2px rgba(46, 78, 164, 0.3);
}

.login-input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 14px;
}

.login-password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s;
}

.login-password-toggle:hover {
  color: var(--highlight-blue);
}

.login-forgot-password {
  text-align: right;
  margin: -10px 0 20px;
}

.login-forgot-link {
  color: #377cfa;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
}

.login-forgot-link:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(
    90deg,
    var(--accent-blue),
    var(--accent-blue-dark)
  );
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  -webkit-transform: translateY(-1px);
  -moz-transform: translateY(-1px);
  -ms-transform: translateY(-1px);
  -o-transform: translateY(-1px);
}

.login-button:disabled {
  opacity: 0.4;
  pointer-events: none;
}

.login-switch {
  text-align: center;
  font-size: 13px;
  color: var(--text-muted);
  margin-top: 20px;
}

.login-switch-link {
  color: #377cfa;
  text-decoration: none;
  font-weight: 500;
}

.login-switch-link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .login-form {
    padding: 25px;
  }
}

/* ========================= */
/* ===== Reset Password Page ======= */
/* ========================= */

.reset-nav-links {
  display: flex;
  align-items: center;
  gap: 25px;
}

.reset-nav-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.reset-main {
  background: radial-gradient(
    circle at center,
    rgba(0, 98, 255, 0.2) 0%,
    transparent 50%
  );
  background-color: var(--primary-bg);
  min-height: 85vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-primary);
  padding: 20px;
}

.reset-form {
  width: 100%;
  max-width: 450px;
  padding: 30px;
}

.reset-heading {
  text-align: center;
  font-size: 28px;
  margin-bottom: 15px;
  font-weight: 600;
}

.reset-subheading {
  text-align: center;
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 30px;
}

.reset-input-group {
  margin-bottom: 25px;
}

.reset-input-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.reset-input-container {
  position: relative;
}

.reset-input-field {
  width: 100%;
  padding: 12px 15px 12px 40px;
  background-color: var(--input-bg);
  color: var(--text-primary);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.reset-input-field:focus {
  outline: none;
  border-color: var(--highlight-blue);
  box-shadow: 0 0 0 2px rgba(46, 78, 164, 0.3);
}

.reset-input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 14px;
}

.reset-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(
    90deg,
    var(--accent-blue),
    var(--accent-blue-dark)
  );
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.reset-switch {
  text-align: center;
  font-size: 13px;
  color: var(--text-muted);
  margin-top: 20px;
}

.reset-switch-link {
  color: #377cfa;
  text-decoration: none;
  font-weight: 500;
}

.reset-switch-link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .reset-form {
    padding: 25px;
  }
}

/* Pricing Page */
.pricing-page-parent {
  height: calc(100vh - 73px);
}

/* Signup Success Page */
.success-msg-container {
  max-width: 300px;
  margin: 240px auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Manage Subscription Page */
.current-plan {
  position: relative;
  background-color: #111319;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 20px;
}

.title {
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 12px;
  margin-top: 12px;
}

.current-plan .label {
  margin-bottom: 12px;
  font-size: 18px;
  text-transform: uppercase;
}

.current-plan .features,
.current-plan .features li {
  margin-bottom: 0;
}

.plans.manage-plans .plan {
  flex: 1;
}

.content {
  display: flex;
  flex-direction: column;
  padding: 20px 200px;
}

.privacy-policy-title {
  font-weight: 500;
  font-size: 32px;
  margin-bottom: 12px;
}

.privacy-policy-subtitle {
  font-weight: 500;
  font-size: 24px;
  margin-bottom: 12px;
}

.privacy-policy-item {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 12px;
  color: var(--text-secondary);
}

.privacy-policy-item span,
.privacy-policy-list span {
  font-weight: 500;
}

.privacy-policy-list {
  margin-left: 12px;
  color: var(--text-secondary);
  margin-bottom: 12px;
}
