const EXTENSION_ID = "inbdbhkbdaficlnfckaipdjppfapfdmi";

document.addEventListener("DOMContentLoaded", () => {
  const form = document.querySelector("#signup-form");
  const termsBox = document.querySelector("#terms");
  const errorEl = document.querySelector("#flash-message.error");
  const successEl = document.querySelector("#flash-message.success");
  const signupBtn = document.querySelector("#signup-button");
  const btnText = document.querySelector(".btn-text");
  const spinner = document.querySelector("#spinner");

  form.addEventListener("submit", async (e) => {
    e.preventDefault();
    errorEl.textContent = ""; // clear old errors
    successEl.textContent = ""; // clear old errors

    if (!termsBox.checked) {
      errorEl.textContent = "You must agree to the Terms & Privacy to sign up.";
      return;
    }

    const formData = new FormData(form);
    const payload = {
      firstName: formData.get("firstName"),
      lastName: formData.get("lastName"),
      email: formData.get("email"),
      phoneNumber: formData.get("phone"),
      password: formData.get("password"),
    };

    try {
      spinner.classList.remove("hidden");
      btnText.textContent = "Loading..";
      signupBtn.disabled = true;

      const res = await fetch("https://lowballer-api.vercel.app/api/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (!res.ok) {
        errorEl.textContent = data.error || "Sign Up Failed";
        errorEl.style.display = "block";
      }

      if (
        typeof chrome !== "undefined" &&
        chrome.runtime &&
        typeof chrome.runtime.sendMessage === "function"
      ) {
        try {
          chrome.runtime.sendMessage(
            EXTENSION_ID,
            {
              type: "SET_TOKEN",
              token: data.accessToken,
              user: data.user,
            },
            (response) => {
              if (chrome.runtime.lastError) {
                errorEl.textContent =
                  chrome.runtime.lastError.message || "Sign in failed";
                errorEl.style.display = "block";
                console.error(
                  "Message failed:",
                  chrome.runtime.lastError.message
                );
                return;
              }
              console.log("Token sent to extension:", response);
              localStorage.setItem("authToken", data.accessToken);
              localStorage.setItem("user", JSON.stringify(data.user));
              successEl.textContent = "Sign In successful!";
              successEl.style.display = "block";
              window.location.href = "/";
            }
          );
        } catch (error) {
          errorEl.textContent = err.message || "Sign Up Failed";
          errorEl.style.display = "block";
          console.log(error);
        }
      } else {
        console.error("Extension not available or chrome.runtime is undefined");
        errorEl.textContent = "Sign Up Failed";
        errorEl.style.display = "block";
      }
    } catch (err) {
      errorEl.textContent = err.message;
      errorEl.style.display = "block";
    } finally {
      spinner.classList.add("hidden");
      btnText.textContent = "Sign Up";
      signupBtn.disabled = false;
    }
  });
});
