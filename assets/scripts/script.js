const EXTENSION_ID = "inbdbhkbdaficlnfckaipdjppfapfdmi";
document.addEventListener("DOMContentLoaded", function () {
  const hamburgerBtn = document.querySelector(".hamburger-btn");
  const navLinks = document.querySelector(".nav-links");
  const dropdownBtns = document.querySelectorAll(".dropdown-btn");
  const plans = document.querySelectorAll(".plan");

  // Toggle mobile menu
  hamburgerBtn.addEventListener("click", function (e) {
    e.stopPropagation(); // Prevent document click from closing immediately
    navLinks.classList.toggle("active");
    const icon = this.querySelector("i");
    icon.classList.toggle("fa-bars");
    icon.classList.toggle("fa-times");
  });

  // Close menu when clicking outside
  document.addEventListener("click", function (e) {
    if (!navLinks.contains(e.target) && e.target !== hamburgerBtn) {
      navLinks.classList.remove("active");
      const icon = hamburgerBtn.querySelector("i");
      icon.classList.remove("fa-times");
      icon.classList.add("fa-bars");
    }
  });

  // Handle dropdown clicks on mobile
  dropdownBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      if (window.innerWidth <= 480) {
      }
      this.parentElement.classList.toggle("active");
    });
  });

  window.addEventListener("scroll", function () {
    const nav = document.querySelector("nav");
    if (window.scrollY > 50) {
      nav.classList.add("scrolled");
    } else {
      nav.classList.remove("scrolled");
    }
  });

  const planButtons = document.querySelectorAll(".plan .btn");
  planButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.stopPropagation();
      plans.forEach((p) => p.classList.remove("highlight"));
      this.closest(".plan").classList.add("highlight");
    });
  });

  const userJson = localStorage.getItem("user");
  if (!userJson) return;

  const user = JSON.parse(userJson);

  // 2. Update the dropdown button text
  const dropdownBtn = document.querySelector(".dropdown-btn");
  dropdownBtn.textContent = user.firstName + " ▾" || "Account ▾";

  // 3. Replace the menu items with a single “Logout”
  const menu = document.querySelector(".dropdown-menu");
  menu.innerHTML = `<div>
    <a href="/manage-subscription">Manage Subscription</a>
    <a href="#" id="logout-btn">Logout</a>
  </div>`;

  // 4. Handle the logout click
  document.getElementById("logout-btn").addEventListener("click", async (e) => {
    e.preventDefault();

    localStorage.removeItem("authToken");
    localStorage.removeItem("user");

    if (
      typeof chrome !== "undefined" &&
      chrome.runtime &&
      typeof chrome.runtime.sendMessage === "function"
    ) {
      chrome.runtime.sendMessage(
        EXTENSION_ID,
        { type: "LOGOUT" },
        (response) => {
          console.log("Extension logout response:", response);
          window.location.href = "/";
        }
      );
    }
  });

  const userTier = user?.tier || null;

  // Stripe checkout links
  const stripeLinks = {
    pro: "https://buy.stripe.com/9B628qerWea9bBwdmj7AI00",
    reseller: "https://buy.stripe.com/28E9ASbfK2rreNI0zx7AI01",
  };

  document.querySelectorAll(".plan").forEach((planCard) => {
    const btn = planCard.querySelector("a.btn");
    const planType = planCard.getAttribute("data-plan");

    if (!btn) return;

    if (planType === userTier) {
      // Mark current plan
      btn.textContent = "Current Plan";
      btn.setAttribute("disabled", "true");
      btn.classList.add("disabled-btn");
      btn.removeAttribute("href");
    } else if (userTier && stripeLinks[planType]) {
      // If logged in with a different plan, allow upgrade
      btn.setAttribute("href", stripeLinks[planType]);
      btn.setAttribute("target", "_blank");
    } else {
      // Not logged in or unknown tier
      btn.setAttribute("href", "/login");
    }
  });
});
