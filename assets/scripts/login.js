const EXTENSION_ID = "inbdbhkbdaficlnfckaipdjppfapfdmi";
document.addEventListener("DOMContentLoaded", () => {
  const form = document.querySelector("#login-form");
  const errorEl = document.querySelector("#flash-message.error");
  const successEl = document.querySelector("#flash-message.success");
  const loginBtn = document.querySelector("#login-button");
  const btnText = document.querySelector(".btn-text");
  const spinner = document.querySelector("#spinner");

  form.addEventListener("submit", async (e) => {
    e.preventDefault();
    errorEl.textContent = ""; // clear old errors
    successEl.textContent = ""; // clear old errors

    const formData = new FormData(form);
    const payload = {
      email: formData.get("email"),
      password: formData.get("password"),
      loginType: "extension",
    };

    try {
      spinner.classList.remove("hidden");
      btnText.textContent = "Loading..";
      loginBtn.disabled = true;

      const res = await fetch("https://lowballer-api.vercel.app/api/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (!res.ok) {
        errorEl.textContent = data.error || "Sign in failed";
        errorEl.style.display = "block";
      }

      if (
        typeof chrome !== "undefined" &&
        chrome.runtime &&
        typeof chrome.runtime.sendMessage === "function"
      ) {
        try {
          chrome.runtime.sendMessage(
            EXTENSION_ID,
            {
              type: "SET_TOKEN",
              token: data.accessToken,
              user: data.user,
            },
            (response) => {
              if (chrome.runtime.lastError) {
                errorEl.textContent = chrome.runtime.lastError.message;
                errorEl.style.display = "block";
                console.error(
                  "Message failed:",
                  chrome.runtime.lastError.message
                );
                return;
              }
              console.log("Token sent to extension:", response);
              localStorage.setItem("authToken", data.accessToken);
              localStorage.setItem("user", JSON.stringify(data.user));
              successEl.textContent = "Sign In successful!";
              successEl.style.display = "block";
              window.location.href = "/";
            }
          );
        } catch (error) {
          errorEl.textContent = error;
          errorEl.style.display = "block";
          console.log(error);
        }
      } else {
        errorEl.textContent =
          "Extension not available or chrome.runtime is undefined";
        errorEl.style.display = "block";
        console.warn("Extension not available or chrome.runtime is undefined");
      }
    } catch (err) {
      errorEl.textContent = err.message;
      errorEl.style.display = "block";
    } finally {
      spinner.classList.add("hidden");
      btnText.textContent = "Sign In";
      loginBtn.disabled = false;
    }
  });
});
