<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pricing - Lowballer</title>

    <!-- Open Graph / Social Media Preview Tags -->
    <meta
      property="og:title"
      content="Lowballer - AI-Powered Marketplace Negotiation"
    />
    <meta
      property="og:description"
      content="Lowballer leverages cutting-edge AI models to analyze listings, calculate depreciation, and determine the best buy price."
    />
    <meta property="og:image" content="/lowballer-preview.png" />
    <meta property="og:type" content="website" />

    <link rel="stylesheet" href="/assets/styles/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <script>
      (() => {
        const token = localStorage.getItem("authToken");
        if (!token) {
          window.location.replace("/");
        }
      })();
    </script>
    <script src="https://js.stripe.com/v3/"></script>
  </head>
  <body>
    <nav aria-label="Main navigation">
      <a href="/" class="logo-link">
        <div class="logo">Lowballer</div>
      </a>

      <!-- Hamburger Menu Button (hidden by default) -->
      <button class="hamburger-btn" aria-label="Toggle navigation">
        <i class="fas fa-bars"></i>
      </button>

      <!-- Navigation Links -->
      <div class="nav-links">
        <a href="#pricing">Pricing</a>
        <a href="https://discord.gg/WERQJ4BXMY" target="_blank">Discord</a>
        <div class="dropdown">
          <button class="dropdown-btn">Account ▾</button>
          <div class="dropdown-menu">
            <a href="/signup">Sign Up</a>
            <a href="/login">Sign In</a>
          </div>
        </div>
      </div>
    </nav>

    <main class="pricing-page-parent">
      <section id="pricing" class="pricing-section">
        <div class="pricing-wrapper">
          <div class="pricing-content">
            <h1 class="pricing-section_heading">Choose Your Plan</h1>
            <p class="subtitle">
              AI Vehicle Analysis. AI-Generated Counteroffers.
              Aggression-Calibrated Slider.
            </p>

            <div class="plans">
              <!-- Free Plan -->
              <div class="plan" data-plan="free">
                <div class="label">Free Plan</div>
                <h3>Free Plan</h3>
                <div class="price">
                  $0 <span style="font-size: 1rem">/month</span>
                </div>
                <ul class="features">
                  <li>1 Free Analysis/day</li>
                </ul>
                <a href="/signup/success" class="btn">Select Plan</a>
              </div>

              <!-- Pro Plan -->
              <div class="plan highlight" data-plan="pro">
                <div class="label">Most Popular</div>
                <h3>Pro Plan</h3>
                <div class="price">
                  $8 <span style="font-size: 1rem">/month</span>
                </div>
                <ul class="features">
                  <li>150 Free Analysis/month</li>
                </ul>
                <a
                  href="https://buy.stripe.com/9B628qerWea9bBwdmj7AI00"
                  class="btn"
                  >Select Plan</a
                >
              </div>

              <!-- Standard Plan -->
              <div class="plan" data-plan="reseller">
                <div class="label">Reseller Plan</div>
                <h3>Reseller Plan</h3>
                <div class="price">
                  $20 <span style="font-size: 1rem">/month</span>
                </div>
                <ul class="features">
                  <li>350 Free Analysis/month</li>
                </ul>
                <a
                  href="https://buy.stripe.com/28E9ASbfK2rreNI0zx7AI01"
                  class="btn"
                  >Select Plan</a
                >
              </div>
            </div>
          </div>
          <a href="/" class="link">Continue for Free</a>
        </div>
      </section>
    </main>
    <script src="/assets/scripts/script.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const userJson = localStorage.getItem("user");
        if (!userJson) return;
        let user;
        try {
          user = JSON.parse(userJson);
        } catch {
          return;
        }
        if (!user.email) return;

        // Use the “real” selector you saw in DevTools:
        const resellerAnchor = document.querySelector(
          '.plan[data-plan="reseller"] a.btn'
        );
        if (resellerAnchor) {
          const oldHref = resellerAnchor.getAttribute("href");
          const sep = oldHref.includes("?") ? "&" : "?";
          resellerAnchor.setAttribute(
            "href",
            oldHref + sep + "prefilled_email=" + encodeURIComponent(user.email)
          );
        }

        const proAnchor = document.querySelector(
          '.plan[data-plan="pro"] a.btn'
        );
        if (proAnchor) {
          const oldHref = proAnchor.getAttribute("href");
          const sep = oldHref.includes("?") ? "&" : "?";
          proAnchor.setAttribute(
            "href",
            oldHref + sep + "prefilled_email=" + encodeURIComponent(user.email)
          );
        }
      });
    </script>
  </body>
</html>
