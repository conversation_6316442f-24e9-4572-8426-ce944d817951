<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Resources - Lowballer</title>

    <!-- Open Graph / Social Media Preview Tags -->
    <meta
      property="og:title"
      content="Lowballer - AI-Powered Marketplace Negotiation"
    />
    <meta
      property="og:description"
      content="Lowballer leverages cutting-edge AI models to analyze listings, calculate depreciation, and determine the best buy price."
    />
    <meta property="og:image" content="/lowballer-preview.png" />
    <meta property="og:type" content="website" />

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Lowballer - AI-Powered Marketplace Negotiation"
    />
    <meta
      name="twitter:description"
      content="Lowballer leverages cutting-edge AI models to analyze listings, calculate depreciation, and determine the best buy price."
    />
    <meta name="twitter:image" content="/lowballer-preview.png" />

    <link rel="stylesheet" href="/assets/styles/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      @media (min-width: 769px) {
        .menu-toggle {
          display: none !important;
        }
      }
    </style>
  </head>
  <body>
    <nav>
      <div class="logo">
        <img
          src="/assets/icon/logo.svg"
          alt="Lowballer logo"
          width="80"
          height="80"
        />
        Lowballer
      </div>
      <button class="menu-toggle" aria-label="Toggle menu">
        <i class="fas fa-bars"></i>
      </button>
      <div class="nav-links">
        <a href="index.html">Home</a>
        <a href="pricing.html">Pricing</a>
        <a href="resources.html" class="active">Resources</a>
        <a href="#" class="install-button">Add to Chrome</a>
      </div>
    </nav>

    <main class="hero">
      <h1>Need Help?</h1>
      <p>
        Need help with Lowballer? Join our discord server to get in touch with
        our team & connect with other lowballers ⛹️‍♂️
      </p>
      <div class="cta-group">
        <a
          href="https://discord.gg/WERQJ4BXMY"
          class="cta-button discord-button"
          target="_blank"
        >
          <i class="fab fa-discord"></i>
          Join our Discord!
        </a>
      </div>
    </main>

    <style>
      .discord-button {
        background-color: #5865f2;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 2rem;
        font-size: 1.2rem;
        font-weight: 600;
        border-radius: 12px;
        transition: background-color 0.2s;
      }

      .discord-button:hover {
        background-color: #4752c4;
      }

      .discord-button i {
        font-size: 1.4rem;
      }
    </style>
    <script>
      const menuToggle = document.querySelector(".menu-toggle");
      const navLinks = document.querySelector(".nav-links");

      menuToggle.addEventListener("click", () => {
        navLinks.classList.toggle("active");
        const icon = menuToggle.querySelector("i");
        if (navLinks.classList.contains("active")) {
          icon.classList.remove("fa-bars");
          icon.classList.add("fa-times");
        } else {
          icon.classList.remove("fa-times");
          icon.classList.add("fa-bars");
        }
      });
    </script>
  </body>
</html>
