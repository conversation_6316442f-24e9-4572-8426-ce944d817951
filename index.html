<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Lowballer - AI-Powered Marketplace Negotiation</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/icon/logo.svg" />
    <link rel="icon" type="image/png" href="/assets/icon/logo.svg" />
    <link rel="apple-touch-icon" href="/assets/icon/logo.svg" />

    <!-- Open Graph / Social Media Preview Tags -->
    <meta
      property="og:title"
      content="Lowballer - AI-Powered Marketplace Negotiation"
    />
    <meta
      property="og:description"
      content="Lowballer leverages cutting-edge AI models to analyze listings, calculate depreciation, and determine the best buy price."
    />
    <meta
      property="og:image"
      content="https://lowballer.co/lowballer-preview.png"
    />
    <meta property="og:url" content="https://lowballer.co" />
    <meta property="og:type" content="website" />

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Lowballer - AI-Powered Marketplace Negotiation"
    />
    <meta
      name="twitter:description"
      content="Lowballer leverages cutting-edge AI models to analyze listings, calculate depreciation, and determine the best buy price."
    />
    <meta
      name="twitter:image"
      content="https://lowballer.co/lowballer-preview.png"
    />

    <link rel="stylesheet" href="/assets/styles/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body>
    <nav aria-label="Main navigation">
      <a href="/" class="logo-link">
        <div class="logo">Lowballer</div>
      </a>

      <!-- Hamburger Menu Button (hidden by default) -->
      <button class="hamburger-btn" aria-label="Toggle navigation">
        <i class="fas fa-bars"></i>
      </button>

      <!-- Navigation Links -->
      <div class="nav-links">
        <a href="#pricing">Pricing</a>
        <a href="https://discord.gg/WERQJ4BXMY" target="_blank">Discord</a>
        <div class="dropdown">
          <button class="dropdown-btn">Account ▾</button>
          <div class="dropdown-menu">
            <a href="/signup">Sign Up</a>
            <a href="/login">Sign In</a>
          </div>
        </div>
      </div>
    </nav>

    <section class="hero-section">
      <div class="hero-headingContainer">
        <img src="/assets/icon/logo.svg" alt="logo" class="hero-icon" />
        <h1 class="hero-heading">
          Your AI-Powered Lowballer<br />
          <span class="hero-highlight"
            >Automate your analysis. Lowball like a pro.</span
          >
        </h1>
      </div>
      <h2 class="hero-subheading">
        Assess market value. Audit quality. Counter with precision.
      </h2>
      <p class="hero-subheading-secondary"></p>
      <div class="cta-group">
        <a
          href="https://chromewebstore.google.com/detail/lowballer/inbdbhkbdaficlnfckaipdjppfapfdmi"
          class="cta-button"
        >
          <img
            src="https://www.google.com/chrome/static/images/chrome-logo.svg"
            alt="Chrome logo"
          />
          Add to Chrome
        </a>
        <a href="#pricing" class="secondary-button"> See pricing </a>
      </div>
      <div class="hero-note"></div>
    </section>

    <section class="feature-section">
      <div class="feature-container">
        <div class="feature-media">
          <iframe
            class="feature-video"
            src="https://www.youtube.com/embed/USS5WaW6Hz8?autoplay=1&mute=1&controls=1&loop=1&playlist=USS5WaW6Hz8"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            title="Feature Demo Placeholder"
          ></iframe>
        </div>
      </div>
    </section>

    <section class="integration-section">
      <div class="integration-box">
        <div class="icons">
          <i class="fa-solid fa-peace"></i>
          <i class="fa-brands fa-facebook"></i>
        </div>
        <h3>Built for Craigslist & Facebook</h3>
        <p>
          Seamlessly integrates with your Craigslist and Facebook Marketplace,
          making it easy to <br />
          analyze, compare, and negotiate listings.
        </p>
      </div>
    </section>

    <section id="pricing" class="pricing-section">
      <div class="pricing-wrapper">
        <div class="pricing-content">
          <h1 class="pricing-section_heading">Choose Your Plan</h1>
          <p class="subtitle">
            AI Vehicle Analysis. AI-Generated Counteroffers.
            Aggression-Calibrated Slider.
          </p>

          <div class="plans">
            <!-- Free Plan -->
            <div class="plan" data-plan="free">
              <div class="label">Free Plan</div>
              <h3>Free Plan</h3>
              <div class="price">
                $0 <span style="font-size: 1rem">/month</span>
              </div>
              <ul class="features">
                <li>1 Free Analysis/day</li>
              </ul>
              <a href="/signup" class="btn">Get started</a>
            </div>

            <!-- Pro Plan -->
            <div class="plan highlight" data-plan="pro">
              <div class="label">Most Popular</div>
              <h3>Pro Plan</h3>
              <div class="price">
                $8 <span style="font-size: 1rem">/month</span>
              </div>
              <ul class="features">
                <li>150 Free Analysis/month</li>
              </ul>
              <a href="/signup" class="btn">Get started</a>
            </div>

            <!-- Standard Plan -->
            <div class="plan" data-plan="reseller">
              <div class="label">Reseller Plan</div>
              <h3>Reseller Plan</h3>
              <div class="price">
                $20 <span style="font-size: 1rem">/month</span>
              </div>
              <ul class="features">
                <li>350 Free Analysis/month</li>
              </ul>
              <a href="/signup" class="btn">Get started</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="final-cta-section">
      <div class="final-cta-container">
        <h2>Supercharge your shopping with AI 🔥</h2>
        <p class="description">
          Why waste time comparing listings? One click gets you real-time price
          analysis, depreciation insights, and AI-crafted counteroffers.
        </p>
        <a
          href="https://chromewebstore.google.com/detail/lowballer/inbdbhkbdaficlnfckaipdjppfapfdmi"
          class="final-cta-button"
        >
          <img
            src="https://www.google.com/chrome/static/images/chrome-logo.svg"
            alt="Chrome logo"
          />
          Add to Chrome for free
        </a>
        <p class="disclaimer">
          Lowballer is currently in limited-access beta. We're collecting real
          feedback <br />
          as we refine the experience with more sites. Join early and help shape
          the experience!
        </p>
      </div>
    </section>

    <footer class="site-footer">
      <div class="footer-content">
        <div class="footer-left">
          <div class="footer-left-up">
            <img src="/assets/icon/logo.svg" alt="Lowballer logo" />
            <h1 class="footer-logo">Lowballer</h1>
          </div>
          <span class="copyright"
            >© 2025 Lowballer || All rights reserved!</span
          >
        </div>
        <div class="footer-right">
          <a href="https://discord.gg/WERQJ4BXMY" target="_blank"
            >Discord community</a
          >
          <a href="/privacy-policy">Privacy Policy & TOS</a>
        </div>
      </div>
    </footer>

    <script src="/assets/scripts/script.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const userJson = localStorage.getItem("user");
        if (!userJson) return;
        let user;
        try {
          user = JSON.parse(userJson);
        } catch {
          return;
        }
        if (!user.email) return;

        // Use the “real” selector you saw in DevTools:
        const resellerAnchor = document.querySelector(
          '.plan[data-plan="reseller"] a.btn'
        );
        if (resellerAnchor) {
          const oldHref = resellerAnchor.getAttribute("href");
          const sep = oldHref.includes("?") ? "&" : "?";
          resellerAnchor.setAttribute(
            "href",
            oldHref + sep + "prefilled_email=" + encodeURIComponent(user.email)
          );
        }

        const proAnchor = document.querySelector(
          '.plan[data-plan="pro"] a.btn'
        );
        if (proAnchor) {
          const oldHref = proAnchor.getAttribute("href");
          const sep = oldHref.includes("?") ? "&" : "?";
          proAnchor.setAttribute(
            "href",
            oldHref + sep + "prefilled_email=" + encodeURIComponent(user.email)
          );
        }
      });
    </script>
  </body>
</html>
